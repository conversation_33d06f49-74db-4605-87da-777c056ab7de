package id.co.bri.brimo.payment.feature.briva.ui.confirmation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.BrivaConfirmationRoute
import id.co.bri.brimo.payment.core.common.launchAndCollectIn
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ImageAsync
import id.co.bri.brimo.payment.core.design.component.Notice
import id.co.bri.brimo.payment.core.design.component.NoticeType
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_27AE60
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse
import id.co.bri.brimo.payment.feature.briva.data.model.BrivaModel
import id.co.bri.brimo.payment.feature.briva.ui.base.BrivaAccountBottomSheet
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import id.co.bri.brimo.payment.feature.brizzi.ui.process.ProcessDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun BrivaConfirmationScreen(
    navigation: (BrivaConfirmationNavigation) -> Unit = {},
    brivaConfirmationViewModel: BrivaConfirmationViewModel = koinViewModel()
) {
    val accountList by brivaConfirmationViewModel.accountList.collectAsStateWithLifecycle()

    if (brivaConfirmationViewModel.brivaModel != null) {
        BrivaConfirmationContent(
            state = BrivaConfirmationState(
                brivaConfirmationRoute = brivaConfirmationViewModel.brivaConfirmationRoute,
                brivaModel = brivaConfirmationViewModel.brivaModel,
                accountList = accountList.orEmpty(),
                brivaPayment = brivaConfirmationViewModel.brivaPayment
            ),
            event = brivaConfirmationViewModel::handleEvent,
            navigation = navigation
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BrivaConfirmationContent(
    state: BrivaConfirmationState,
    event: (BrivaConfirmationEvent) -> Unit = {},
    navigation: (BrivaConfirmationNavigation) -> Unit = {}
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // Global
    val saved = if (state.brivaModel.brivaInquiry != null) {
        state.brivaModel.brivaInquiry.saved.orEmpty()
    } else {
        state.brivaModel.brivaConfirmation?.saved.orEmpty()
    }
    var nameField by rememberSaveable { mutableStateOf(saved) }
    var noteField by rememberSaveable { mutableStateOf("") }
    val counterNote by remember {
        derivedStateOf {
            "${noteField.length}/30"
        }
    }
    var selectedPin by rememberSaveable { mutableStateOf("") }
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.firstOrNull())
    }
    val notEnough = (selectedAccount?.balance?.toDoubleOrNull() ?: 0.0) <
        (state.brivaModel.brivaInquiry?.payAmount?.toDoubleOrNull() ?: 0.0) &&
        !state.brivaConfirmationRoute.fastMenu
    var switch by rememberSaveable { mutableStateOf(false) }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Process
    var processDialog by rememberSaveable { mutableStateOf(false) }
    if (processDialog) {
        Dialog(
            onDismissRequest = { processDialog = false },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                ProcessDialog()
            }
        }
    }

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            val title = if (state.brivaModel.brivaInquiry != null) {
                state.brivaModel.brivaInquiry.billingDetailOpen?.firstOrNull()?.title
            } else {
                state.brivaModel.brivaConfirmation?.billingDetail?.title
            }
            val saveAs = if (switch && nameField.isEmpty()) {
                title
            } else {
                nameField
            }
            event(
                BrivaConfirmationEvent.Payment(
                    pin = selectedPin,
                    saveAs = saveAs.orEmpty(),
                    accountNumber = selectedAccount?.account.orEmpty(),
                    note = noteField
                )
            )
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Pin
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        val title = if (state.brivaModel.brivaInquiry != null) {
                            state.brivaModel.brivaInquiry.billingDetailOpen?.firstOrNull()?.title
                        } else {
                            state.brivaModel.brivaConfirmation?.billingDetail?.title
                        }
                        val saveAs = if (switch && nameField.isEmpty()) {
                            title
                        } else {
                            nameField
                        }
                        event(
                            BrivaConfirmationEvent.Payment(
                                pin = selectedPin,
                                saveAs = saveAs.orEmpty(),
                                accountNumber = selectedAccount?.account.orEmpty(),
                                note = noteField
                            )
                        )
                    }
                )
            }
        }
    }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            BrivaAccountBottomSheet(
                nominal = state.brivaModel.brivaInquiry?.payAmount.orEmpty(),
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(BrivaConfirmationEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.brivaConfirmationRoute.fastMenu
            )
        }
    }

    // Briva Payment
    LaunchedEffect(Unit) {
        state.brivaPayment.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess { data ->
                    progressDialog = false
                    val brivaData = data.serialize().orEmpty()
                    if (brivaData.isEmpty()) {
                        errorBottomSheet = true
                    } else {
                        scope.launch {
                            processDialog = true
                            delay(1000)
                            navigation(BrivaConfirmationNavigation.Payment(brivaData))
                        }
                    }
                }
                .onError { e ->
                    progressDialog = false
                    if (e is MessageException && e.errorSnackbar()) {
                        if (e.description.contains("pin", true)) {
                            pinMessage = e.description
                        } else {
                            pinDialog = false
                            scope.launch {
                                snackbarType = SnackbarType.ERROR
                                snackbarHostState.showSnackbar(e.description)
                            }
                        }
                    } else {
                        error = e
                        errorBottomSheet = true
                    }
                }
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                if (state.brivaModel.brivaInquiry != null) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 16.dp, top = 16.dp, end = 16.dp)
                            .clickable {
                                accountBottomSheet = true
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AsyncImage(
                            model = selectedAccount?.imagePath.orEmpty(),
                            contentDescription = null,
                            modifier = Modifier
                                .width(58.dp)
                                .height(36.dp),
                            placeholder = painterResource(id = R.drawable.thumbnail),
                            error = painterResource(id = R.drawable.thumbnail),
                            contentScale = ContentScale.Inside
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        if (state.brivaConfirmationRoute.fastMenu) {
                            Text(
                                text = selectedAccount?.accountString.orEmpty(),
                                modifier = Modifier.weight(1f),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        } else {
                            var hide by rememberSaveable { mutableStateOf(false) }
                            val icon = if (hide) {
                                R.drawable.icon_hide_eye
                            } else {
                                R.drawable.icon_unhide_eye
                            }
                            val accountString = selectedAccount?.accountString.orEmpty()
                            val account = if (hide) {
                                "${accountString.take(4)} **** ****${accountString.takeLast(4)}"
                            } else {
                                accountString
                            }
                            val balance = if (hide) {
                                "••••••"
                            } else {
                                selectedAccount?.balanceString.orEmpty()
                            }
                            val balanceError = selectedAccount?.balance == null
                            val color = if (balanceError || notEnough) Color_E84040 else Color.Black

                            Column(modifier = Modifier.weight(1f)) {
                                Row(
                                    modifier = Modifier.clickable {
                                        hide = !hide
                                    },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = account,
                                        style = MaterialTheme.typography.labelSmall
                                    )

                                    if (!balanceError) {
                                        Spacer(modifier = Modifier.width(4.dp))

                                        Image(
                                            painter = painterResource(icon),
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp),
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                }

                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    if (!balanceError) {
                                        Text(
                                            text = selectedAccount?.currency.orEmpty() + balance,
                                            color = color,
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.width(8.dp))
                                    }

                                    if (balanceError) {
                                        Text(
                                            text = "Gagal memuat saldo",
                                            color = Color_E84040,
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }
                            }
                        }

                        Spacer(modifier = Modifier.width(12.dp))

                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                Button(
                    onClick = {
                        pinDialog = true
                    },
                    modifier = Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !notEnough,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color_0054F3,
                        contentColor = Color.White
                    )
                ) {
                    Text(
                        text = "Bayar Sekarang",
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    val amount = if (state.brivaModel.brivaInquiry != null) {
                        state.brivaModel.brivaInquiry.payAmountString
                    } else {
                        state.brivaModel.brivaConfirmation?.payAmountString
                    }

                    Text(
                        text = amount.orEmpty(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(BrivaConfirmationNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "Konfirmasi",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "Total Tagihan",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge
                )

                val amount = if (state.brivaModel.brivaInquiry != null) {
                    state.brivaModel.brivaInquiry.payAmountString
                } else {
                    state.brivaModel.brivaConfirmation?.payAmountString
                }

                Text(
                    text = amount.orEmpty(),
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.headlineLarge
                )

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Tujuan Transaksi",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val image = if (state.brivaModel.brivaInquiry != null) {
                            state.brivaModel.brivaInquiry.billingDetailOpen?.firstOrNull()?.iconPath.orEmpty()
                        } else {
                            state.brivaModel.brivaConfirmation?.billingDetail?.iconPath.orEmpty()
                        }
                        val title = if (state.brivaModel.brivaInquiry != null) {
                            state.brivaModel.brivaInquiry.billingDetailOpen?.firstOrNull()?.title.orEmpty()
                        } else {
                            state.brivaModel.brivaConfirmation?.billingDetail?.title.orEmpty()
                        }

                        ImageAsync(
                            context = context,
                            url = image,
                            initial = title,
                            size = 32
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = title,
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(2.dp))

                            val subtitle = if (state.brivaModel.brivaInquiry != null) {
                                state.brivaModel.brivaInquiry.billingDetailOpen?.firstOrNull()?.subtitle
                            } else {
                                state.brivaModel.brivaConfirmation?.billingDetail?.subtitle
                            }

                            val description = if (state.brivaModel.brivaInquiry != null) {
                                state.brivaModel.brivaInquiry.billingDetailOpen?.firstOrNull()?.description
                            } else {
                                state.brivaModel.brivaConfirmation?.billingDetail?.description
                            }

                            Text(
                                text = "$subtitle - $description",
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }

                    if (saved.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(16.dp))

                        Notice(
                            description = "Tersimpan Sebagai $saved",
                            type = NoticeType.INFO,
                        )
                    } else if (state.brivaModel.brivaInquiry != null && !state.brivaConfirmationRoute.fastMenu) {
                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Simpan ke Favorit",
                                modifier = Modifier.weight(1f),
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Switch(
                                checked = switch,
                                onCheckedChange = {
                                    switch = it
                                },
                                modifier = Modifier
                                    .height(24.dp)
                                    .scale(0.75f),
                                colors = SwitchDefaults.colors(
                                    checkedTrackColor = Color_27AE60,
                                    uncheckedTrackColor = Color_E9EEF6,
                                    checkedBorderColor = Color.Transparent,
                                    uncheckedBorderColor = Color.Transparent,
                                    checkedThumbColor = Color.White,
                                    uncheckedThumbColor = Color.White
                                )
                            )
                        }

                        if (switch) {
                            Spacer(modifier = Modifier.height(16.dp))

                            var isFocusedName by rememberSaveable { mutableStateOf(false) }
                            val borderColorName = if (isFocusedName) {
                                Color_0054F3
                            } else {
                                Color.Transparent
                            }

                            TextFieldCustom(
                                value = nameField,
                                onValueChange = {
                                    if (it.length <= 52) {
                                        nameField = it
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(64.dp)
                                    .onFocusChanged { state ->
                                        isFocusedName = state.isFocused
                                    }
                                    .border(1.dp, borderColorName, RoundedCornerShape(16.dp)),
                                textStyle = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.SemiBold
                                ),
                                label = {
                                    Text(text = "Nama Tersimpan")
                                },
                                trailingIcon = if (nameField.isNotEmpty()) {
                                    {
                                        Image(
                                            painter = painterResource(R.drawable.icon_close),
                                            contentDescription = null,
                                            modifier = Modifier
                                                .size(16.dp)
                                                .clickable {
                                                    nameField = ""
                                                },
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                } else {
                                    null
                                },
                                singleLine = true,
                                shape = RoundedCornerShape(16.dp),
                                colors = textFieldColors(),
                                contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Detail Tagihan",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(horizontal = 16.dp, vertical = 12.dp)
                    ) {
                        if (state.brivaModel.brivaInquiry != null) {
                            state.brivaModel.brivaInquiry.billingAmountDetail?.forEachIndexed { index, item ->
                                ItemBilling(
                                    name = item.name.orEmpty(),
                                    value = item.value.orEmpty(),
                                    notLast = index < state.brivaModel.brivaInquiry.billingAmountDetail.size - 1
                                )
                            }
                        } else {
                            var showDetail by rememberSaveable { mutableStateOf(false) }
                            val textDetail by remember {
                                derivedStateOf {
                                    if (showDetail) "Sembunyikan" else "Lihat Detail"
                                }
                            }
                            val iconDetail by remember {
                                derivedStateOf {
                                    if (showDetail) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown
                                }
                            }

                            val firstList =
                                state.brivaModel.brivaConfirmation?.amountDataView?.dropLast(2)
                            val lastList =
                                state.brivaModel.brivaConfirmation?.amountDataView?.takeLast(2)

                            if (showDetail) {
                                firstList?.forEachIndexed { index, item ->
                                    ItemBilling(
                                        name = item.name.orEmpty(),
                                        value = item.value.orEmpty(),
                                        notLast = index < state.brivaModel.brivaConfirmation.amountDataView.size - 1
                                    )
                                }

                                DividerHorizontal()

                                Spacer(modifier = Modifier.height(12.dp))
                            }

                            lastList?.forEachIndexed { index, item ->
                                ItemBilling(
                                    name = item.name.orEmpty(),
                                    value = item.value.orEmpty(),
                                    notLast = index < state.brivaModel.brivaConfirmation.amountDataView.size - 1
                                )
                            }

                            Row(
                                modifier = Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .clickable {
                                        showDetail = !showDetail
                                    },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = textDetail,
                                    modifier = Modifier.padding(start = 4.dp),
                                    color = Color_0054F3,
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.width(4.dp))

                                Icon(
                                    imageVector = iconDetail,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                    tint = Color_0054F3
                                )
                            }
                        }
                    }

                    if (state.brivaModel.brivaConfirmation != null) {
                        Spacer(modifier = Modifier.height(24.dp))

                        Text(
                            text = "Sumber Dana",
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                                .padding(16.dp)
                        ) {
                            AsyncImage(
                                model = state.brivaModel.brivaConfirmation.sourceAccountDataView?.iconPath.orEmpty(),
                                contentDescription = null,
                                modifier = Modifier
                                    .width(58.dp)
                                    .height(36.dp),
                                placeholder = painterResource(id = R.drawable.thumbnail),
                                error = painterResource(id = R.drawable.thumbnail),
                                contentScale = ContentScale.Inside
                            )

                            Spacer(modifier = Modifier.width(16.dp))

                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = state.brivaModel.brivaConfirmation.sourceAccountDataView?.title.orEmpty(),
                                    modifier = Modifier.fillMaxWidth(),
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Text(
                                    text = state.brivaModel.brivaConfirmation.sourceAccountDataView?.description.orEmpty(),
                                    modifier = Modifier.fillMaxWidth(),
                                    color = Color_7B90A6,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }

                    if (state.brivaModel.brivaInquiry != null) {
                        Spacer(modifier = Modifier.height(24.dp))

                        DividerHorizontal()

                        Spacer(modifier = Modifier.height(24.dp))

                        var isFocusedNote by rememberSaveable { mutableStateOf(false) }
                        val borderColorNote = if (isFocusedNote) {
                            Color_0054F3
                        } else {
                            Color.Transparent
                        }

                        TextFieldCustom(
                            value = noteField,
                            onValueChange = {
                                if (it.length <= 30) {
                                    noteField = it
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp)
                                .onFocusChanged { state ->
                                    isFocusedNote = state.isFocused
                                }
                                .border(1.dp, borderColorNote, RoundedCornerShape(16.dp)),
                            textStyle = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            label = {
                                Text(text = "Catatan (Opsional)")
                            },
                            trailingIcon = if (noteField.isNotEmpty()) {
                                {
                                    Image(
                                        painter = painterResource(R.drawable.icon_close),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(16.dp)
                                            .clickable {
                                                noteField = ""
                                            },
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            } else {
                                null
                            },
                            singleLine = true,
                            shape = RoundedCornerShape(16.dp),
                            colors = textFieldColors(),
                            contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = counterNote,
                            modifier = Modifier.align(Alignment.End),
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.systemBars))
                }
            }
        }
    }
}

@Composable
private fun ItemBilling(
    name: String,
    value: String,
    notLast: Boolean
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = name,
            modifier = Modifier.weight(1f),
            color = Color_7B90A6,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = value,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodyMedium
        )
    }

    if (notLast) {
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Preview
@Composable
fun PreviewBrivaConfirmation() {
    MainTheme {
        BrivaConfirmationContent(
            state = BrivaConfirmationState(
                brivaConfirmationRoute = BrivaConfirmationRoute(
                    data = "",
                    fastMenu = false
                ),
                brivaModel = BrivaModel(
                    brivaInquiry = BrivaInquiryResponse(
                        accountList = listOf(
                            AccountResponse(
                                account = "***************",
                                accountString = "0230 0113 7115 507",
                                name = "ADIXXXXXXXXXXXXXXLTI",
                                currency = "Rp",
                                cardNumber = "5221XXXXXXXX7777",
                                cardNumberString = "5221 XXXX XXXX 7777",
                                productType = "BritAma",
                                accountType = "SA",
                                scCode = "TA",
                                default = 0,
                                alias = "",
                                minimumBalance = "50000",
                                limit = "-1",
                                limitString = "",
                                imageName = "",
                                imagePath = "",
                                onHold = false,
                                balance = "9085000",
                                balanceString = "9.085.000,00"
                            )
                        ),
                        billingDetail = listOf(),
                        billingDetailOpen = listOf(
                            BillingResponse(
                                listType = "",
                                iconName = "",
                                iconPath = "",
                                title = "TESTING123",
                                subtitle = "Dummy",
                                description = "8811812345001"
                            )
                        ),
                        billingAmount = listOf(),
                        billingAmountDetail = listOf(
                            DataViewResponse(
                                name = "Nominal",
                                value = "Rp10.000",
                                style = ""
                            ),
                            DataViewResponse(
                                name = "Biaya Admin",
                                value = "Rp1.000",
                                style = ""
                            )
                        ),
                        openPayment = false,
                        isBilling = false,
                        minimumPayment = false,
                        rowDataShow = 1,
                        saved = "",
                        amount = "",
                        amountString = "",
                        minimumAmount = "",
                        minimumAmountString = "",
                        adminFee = "",
                        adminFeeString = "",
                        payAmount = "11000",
                        payAmountString = "Rp11.000",
                        minimumTransaction = "",
                        minimumTransactionString = "",
                        noteEnable = false,
                        pfmCategory = 0,
                        pfmDescription = "",
                        pageTitleString = "",
                        pageButtonString = "",
                        referenceNumber = "",
                        cashback = false
                    ),
                    brivaConfirmation = null,
                    accountNumber = null
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "0230 0113 7115 507",
                        name = "ADIXXXXXXXXXXXXXXLTI",
                        currency = "Rp",
                        cardNumber = "5221XXXXXXXX7777",
                        cardNumberString = "5221 XXXX XXXX 7777",
                        productType = "BritAma",
                        accountType = "SA",
                        scCode = "TA",
                        default = 0,
                        alias = "",
                        minimumBalance = "50000",
                        limit = "-1",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9085000",
                        balanceString = "9.085.000,00"
                    )
                )
            )
        )
    }
}

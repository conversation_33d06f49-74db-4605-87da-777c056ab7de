<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/main"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_x6_half" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_marginTop="@dimen/size_26dp">

        <id.co.bri.brimo.ui.customviews.BounceNestedScrollView
            android:id="@+id/scrollBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/toolbar"
            android:fillViewport="true"
            android:scrollbars="none"
            android:overScrollMode="always"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/space_x1"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_top_banner"
                    android:layout_width="match_parent"
                    android:layout_height="377dp"
                    android:layout_marginHorizontal="@dimen/size_12dp"
                    android:layout_gravity="center_horizontal"
                    app:layout_constraintBaseline_toTopOf="@id/rv_fast_menu">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitXY"
                        android:background="@drawable/bg_login_notif"
                        android:src="@drawable/bg_login_notif"
                        app:shapeAppearanceOverlay="@style/FastMenuWelcomeTextStyle"/>

                    <TextView
                        android:id="@+id/tv_name"
                        style="@style/Body3SmallText.Medium.NeutralBaseWhite"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/size_40dp"
                        android:layout_marginHorizontal="@dimen/space_x3"
                        tools:text="Hai, Sabrina"/>

                    <TextView
                        style="@style/Title4Text.Medium.NeutralBaseWhite"
                        android:layout_width="210dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:layout_marginHorizontal="@dimen/space_x3"
                        android:layout_below="@id/tv_name"
                        android:lineHeight="@dimen/size_28dp"
                        android:text="@string/txt_welcoming_text"/>
                </RelativeLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_fast_menu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    android:layout_marginTop="@dimen/size_35dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="5"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_home_menu_revamp" />

                <!--                <androidx.constraintlayout.widget.Guideline-->
                <!--                    android:id="@+id/guideline"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:orientation="horizontal"-->
                <!--                    app:layout_constraintGuide_percent="0.8" />-->

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_login"
                    style="@style/ButtonPrimaryNewSkin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x7"
                    android:layout_marginHorizontal="@dimen/space_x1"
                    android:layout_marginTop="@dimen/size_15dp"
                    android:layout_marginBottom="@dimen/size_15dp"
                    android:text="@string/action_sign_in"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"
                    app:backgroundTint="@color/selector_button_primary_bg"
                    android:enabled="true" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_promo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="8dp"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never" />
            </LinearLayout>
        </id.co.bri.brimo.ui.customviews.BounceNestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>
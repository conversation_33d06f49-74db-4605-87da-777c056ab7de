<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_new_skin_activity_container">

    <include
        android:layout_marginTop="14dp"
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_marginTop="100dp"
        android:background="@drawable/bg_new_skin_activity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:orientation="vertical"
            android:layout_marginTop="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Buat Password"
                android:inputType="textPassword"
                app:iconColor="@color/text_black_default_ns"
                app:iconDrawable="@drawable/ic_new_skin_eye_close"
                app:iconMode="custom" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="14dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivSpace"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center"
                        android:src="@drawable/checkbox_blue_newskin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_half"
                        android:fontFamily="@font/bri_digital_text_regular"
                        android:text="Tidak mengandung spasi"
                        android:textColor="@color/ns_black500"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivCharacter"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center"
                        android:src="@drawable/checkbox_blue_newskin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_half"
                        android:fontFamily="@font/bri_digital_text_regular"
                        android:text="Minimal 8 Karakter"
                        android:textColor="@color/ns_black500"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivCapital"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center"
                        android:src="@drawable/checkbox_blue_newskin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_half"
                        android:fontFamily="@font/bri_digital_text_regular"
                        android:text="Huruf kapital, huruf kecil, angka, dan simbol"
                        android:textColor="@color/ns_black500"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivDiffPass"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center"
                        android:src="@drawable/checkbox_blue_newskin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_half"
                        android:fontFamily="@font/bri_digital_text_regular"
                        android:text="Tidak sama dengan sebelumnya"
                        android:textColor="@color/ns_black500"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

            <id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView
                android:id="@+id/input_confirm_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_24dp"
                android:hint="Konfirmasi Password"
                android:inputType="textPassword"
                app:iconColor="@color/text_black_default_ns"
                app:iconDrawable="@drawable/ic_new_skin_eye_close"
                app:iconMode="custom" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivSame"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center"
                    android:src="@drawable/checkbox_blue_newskin" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/space_half"
                    android:fontFamily="@font/bri_digital_text_regular"
                    android:text="Password Sama"
                    android:textColor="@color/ns_black500"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/ns_graysoft"
            app:layout_constraintBottom_toTopOf="@id/layout_button"/>

        <LinearLayout
            android:id="@+id/layout_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/space_x2"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_next"
                style="@style/ButtonPrimaryNewSkin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_x7"
                android:text="@string/lanjutkan"
                app:backgroundTint="@color/selector_button_primary_bg"
                android:enabled="false" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
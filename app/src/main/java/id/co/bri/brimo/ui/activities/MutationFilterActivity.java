package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import org.threeten.bp.LocalDate;
import org.threeten.bp.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.GenericChipAdapter;
import id.co.bri.brimo.databinding.ActivityTransactionFilterBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.DurasiModel;
import id.co.bri.brimo.models.TransactionTypeModel;
import id.co.bri.brimo.models.YearModel;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.CalendarMutationFragment;
import id.co.bri.brimo.ui.fragments.ListMonthFragment;
import id.co.bri.brimo.ui.fragments.ListTransactionTypeFragment;
import kotlin.Unit;

public class MutationFilterActivity extends BaseActivity implements View.OnClickListener, ListMonthFragment.SelectMonthYearInterface,
        CalendarMutationFragment.OnSelectDate, ListTransactionTypeFragment.SelectTransactionTypeInterface {

    private ActivityTransactionFilterBinding binding;

    GenericChipAdapter<TransactionTypeModel> chipAdapter;
    protected static DurasiModel currentDuration = null, currentDurationTemp = null;
    private static ArrayList<TransactionTypeModel> transactionType = new ArrayList<>();
    private static List<YearModel> yearList = new ArrayList<>();
    private static ArrayList<AccountModel> mListAccountModel = new ArrayList<>();
    private static String selectedFilterIs = null, transactionTypeName = null, transactionTypeId = null,
            selectedMonth = null, selectedYear = null, startDate = null, endDate = null;
    AccountModel model = null;
    private String selectedMonthInNumber;
    private List<YearModel.MonthModelData> listMonth;
    private String monthNumb;
    private String convertStartDate, convertEndDate;
    private LocalDate localStartDate, localEndDate;

    private static boolean mHilangType;
    private static boolean mIsMutationForRDN;
    private boolean isReset = false;
    private boolean isCheckedRange = false;

    public static void launchIntent(Activity caller, ArrayList<TransactionTypeModel> transactionTypeModelList, List<YearModel> yearModelList,
                                    String selectedFilter, String transactionTypeIds, String month, String year, String startDates, String endDates, Boolean hilangType) {
        Intent intent = new Intent(caller, MutationFilterActivity.class);
        transactionType = transactionTypeModelList;
        yearList = yearModelList;
        currentDuration = new DurasiModel();
        selectedFilterIs = selectedFilter;
        transactionTypeId = transactionTypeIds;
        selectedMonth = month;
        selectedYear = year;
        startDate = startDates;
        endDate = endDates;
        mHilangType = hilangType;
        mIsMutationForRDN = false;
        caller.startActivityForResult(intent, Constant.REQ_DURATION);
    }

    public static void launchIntent(ActivityResultLauncher<Intent> launcher, Context context, ArrayList<AccountModel> accountModels, ArrayList<TransactionTypeModel> transactionTypeModelList, List<YearModel> yearModelList,
                                    String selectedFilter, String transactionTypeIds, String month, String year, String startDates, String endDates, Boolean hilangType) {
        Intent intent = new Intent(context, MutationFilterActivity.class);
        mListAccountModel = accountModels;
        transactionType = transactionTypeModelList;
        yearList = yearModelList;
        currentDuration = new DurasiModel();
        selectedFilterIs = selectedFilter;
        transactionTypeId = transactionTypeIds;
        selectedMonth = month;
        selectedYear = year;
        startDate = startDates;
        endDate = endDates;
        mHilangType = hilangType;
        mIsMutationForRDN = false;
        launcher.launch(intent);
    }

    public static void launchIntent(Activity caller, ArrayList<TransactionTypeModel> transactionTypeModelList, List<YearModel> yearModelList,
                                    String selectedFilter, String transactionTypeIds, String month, String year, String startDates, String endDates, Boolean hilangType, Boolean isMutationForRDN) {
        Intent intent = new Intent(caller, MutationFilterActivity.class);
        transactionType = transactionTypeModelList;
        yearList = yearModelList;
        currentDuration = new DurasiModel();
        selectedFilterIs = selectedFilter;
        transactionTypeId = transactionTypeIds;
        selectedMonth = month;
        selectedYear = year;
        startDate = startDates;
        endDate = endDates;
        mHilangType = hilangType;
        mIsMutationForRDN = isMutationForRDN;
        caller.startActivityForResult(intent, Constant.REQ_DURATION);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTransactionFilterBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        currentDurationTemp = new DurasiModel();

        if (selectedMonth == null) {
            selectedMonth = CalendarHelper.getNameOfCurrentMonth();
            selectedYear = CalendarHelper.getCurrentYear();
        }

        setUpView();

        setOnClickListener();

        binding.rgFilter.setOnCheckedChangeListener((group, checkedId) -> {
           if (binding.rbToday.isChecked()) {
                isReset = false;
                binding.rlSelectMonth.setVisibility(View.GONE);
                binding.llSelectDate.setVisibility(View.GONE);
                currentDuration = CalendarHelper.getDurasiHariIni();
                isCheckedRange = true;
                setEnableButton(transactionTypeName != null);
            } else if (binding.rbSevenDays.isChecked()) {
                isReset = false;
                binding.rlSelectMonth.setVisibility(View.GONE);
                binding.llSelectDate.setVisibility(View.GONE);
                currentDuration = CalendarHelper.getDurasiMingguIni();
                isCheckedRange = true;
                setEnableButton(transactionTypeName != null);
            } else if (binding.rbSelectMonth.isChecked()) {
                isReset = false;
                binding.llSelectDate.setVisibility(View.GONE);
                binding.rlSelectMonth.setVisibility(View.GONE);
                ListMonthFragment fragment = new ListMonthFragment(this, getApplicationContext(), yearList, selectedMonth, selectedYear);
                fragment.show(getSupportFragmentManager(), "");
                if (selectedMonth!=null && selectedYear!=null) {
                    binding.tvMonth.setText(selectedMonth + " " + selectedYear);
                } else binding.tvMonth.setText(CalendarHelper.getNameOfCurrentMonthYear());
                binding.rlSelectMonth.setOnClickListener(this);
                currentDuration = CalendarHelper.getDurasiBulanIni();
                isCheckedRange = true;
                setEnableButton(transactionTypeName != null);
            } else if (binding.rbSelectDate.isChecked()) {
                isReset = false;
                binding.llSelectDate.setVisibility(View.GONE);
                binding.rlSelectMonth.setVisibility(View.GONE);
                selectStartDate();
                if (startDate == null && endDate == null) {
                    currentDuration = null;
                    binding.tvDate.setText(CalendarHelper.getFullDateNow());

                    convertStartDate = CalendarHelper.convertToStringFormat(CalendarHelper.getFullDateNow());
                    convertEndDate = CalendarHelper.convertToStringFormat(CalendarHelper.getFullDateNow());

                    if (convertStartDate != null && !convertStartDate.isEmpty()) {
                        localStartDate = LocalDate.parse(convertStartDate);
                    }

                    if (convertEndDate != null && !convertEndDate.isEmpty()) {
                        localEndDate = LocalDate.parse(convertEndDate);
                    }

                    if (localStartDate != null && localEndDate != null) {
                        currentDurationTemp = new DurasiModel(
                                localStartDate.getDayOfMonth(),
                                localStartDate.getMonthValue(),
                                localStartDate.getYear(),
                                localEndDate.getDayOfMonth(),
                                localEndDate.getMonthValue(),
                                localEndDate.getYear()
                        );
                    } else {
                        currentDurationTemp = null;
                    }

                } else {
                    convertStartDate = CalendarHelper.convertToNewFormat(startDate);
                    convertEndDate = CalendarHelper.convertToNewFormat(endDate);

                    if (convertStartDate != null && !convertStartDate.isEmpty()) {
                        localStartDate = LocalDate.parse(convertStartDate);
                    }

                    if (convertEndDate != null && !convertEndDate.isEmpty()) {
                        localEndDate = LocalDate.parse(convertEndDate);
                    }

                    if (localStartDate != null && localEndDate != null) {
                        currentDuration = new DurasiModel(
                                localStartDate.getDayOfMonth(),
                                localStartDate.getMonthValue(),
                                localStartDate.getYear(),
                                localEndDate.getDayOfMonth(),
                                localEndDate.getMonthValue(),
                                localEndDate.getYear()
                        );

                        String startDateString = currentDuration.getStartDateMutasiStringddMMMyyyy().substring(0, 3) +
                                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(3, 6) +
                                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(6, 11);

                        String endDateString = currentDuration.getEndDateMutasiStringddMMMyyyy().substring(0, 3) +
                                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(3, 6) +
                                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(6, 11);

                        binding.tvDate.setText(startDateString + " - " + endDateString);
                    }
                }
                isCheckedRange = true;
                setEnableButton(transactionTypeName != null);
            }
        });

    }

    private void setupToolbar()  {
        GeneralHelperNewSkin.INSTANCE.setToolbarRightText(this, binding.toolbar.toolbar, getString(R.string.filter), getString(R.string.reset));
    }

    private void setUpView() {
        if (selectedFilterIs != null) {
            if (selectedFilterIs.equalsIgnoreCase(Constant.TODAY_FILTER)) {
                binding.rbToday.setChecked(true);
            } else if (selectedFilterIs.equalsIgnoreCase(Constant.SEVEN_DAY_FILTER)) {
                binding.rbSevenDays.setChecked(true);
            } else if (selectedFilterIs.equalsIgnoreCase(Constant.MONTH_FILTER)) {
                binding.rbSelectMonth.setChecked(true);
                binding.rlSelectMonth.setVisibility(View.VISIBLE);
            } else if (selectedFilterIs.equalsIgnoreCase(Constant.RANGE_FILTER)) {
                binding.rbSelectDate.setChecked(true);
                binding.llSelectDate.setVisibility(View.VISIBLE);
            }
            binding.toolbar.textRight.setVisibility(View.VISIBLE);
            binding.toolbar.textRight.setEnabled(true);
            binding.toolbar.textRight.setOnClickListener(this);
        }

        for (AccountModel item : mListAccountModel) {
            if (item.getIsDefault() == 1) {
                model = item;
                break;
            }
        }

        if (transactionType != null) {
            setAdapterTransaction();
        }

        if (transactionTypeId != null) {
            setTransactionType(transactionTypeId);
        }

        if (selectedMonth != null) {
            setYear(selectedYear, selectedMonth);
        }

        if (startDate != null && endDate != null) {
            convertStartDate = CalendarHelper.convertToNewFormat(startDate);
            convertEndDate = CalendarHelper.convertToNewFormat(endDate);

            if (convertStartDate != null && !convertStartDate.isEmpty()) {
                localStartDate = LocalDate.parse(convertStartDate);
            }

            if (convertEndDate != null && !convertEndDate.isEmpty()) {
                localEndDate = LocalDate.parse(convertEndDate);
            }

            if (localStartDate != null && localEndDate != null) {
                currentDuration = new DurasiModel(
                        localStartDate.getDayOfMonth(),
                        localStartDate.getMonthValue(),
                        localStartDate.getYear(),
                        localEndDate.getDayOfMonth(),
                        localEndDate.getMonthValue(),
                        localEndDate.getYear()
                );

                String startDateString = currentDuration.getStartDateMutasiStringddMMMyyyy().substring(0, 3) +
                        currentDuration.getStartDateMutasiStringddMMMyyyy().substring(3, 6) +
                        currentDuration.getStartDateMutasiStringddMMMyyyy().substring(6, 11);

                String endDateString = currentDuration.getEndDateMutasiStringddMMMyyyy().substring(0, 3) +
                        currentDuration.getEndDateMutasiStringddMMMyyyy().substring(3, 6) +
                        currentDuration.getEndDateMutasiStringddMMMyyyy().substring(6, 11);

                binding.tvDate.setText(startDateString + " - " + endDateString);
            }
        }
        if (mHilangType) {
            binding.llTransaksi.setVisibility(View.GONE);
        } else {
            binding.llTransaksi.setVisibility(View.VISIBLE);
        }

        setEnableButton(selectedFilterIs != null && transactionTypeName != null);
    }

    private void setAdapterTransaction() {

        binding.rvListTransactionType.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        chipAdapter = new GenericChipAdapter<>(
                this,
                transactionType,
                TransactionTypeModel::getTypeDesc,
                TransactionTypeModel::getType,
                category -> {
                    isReset = false;
                    transactionTypeId = category.getType();
                    transactionTypeName = category.getTypeDesc();
                    setEnableButton(isCheckedRange && transactionTypeName != null);
                    return Unit.INSTANCE;
                }

        );
        binding.rvListTransactionType.setAdapter(chipAdapter);
    }

    private void setTransactionType(String typeId) {
        for (int i = 0; i < transactionType.size(); i++) {
            if (typeId.equalsIgnoreCase(transactionType.get(i).getType())) {
                transactionTypeId = transactionType.get(i).getType();
                transactionTypeName = transactionType.get(i).getTypeDesc();
                chipAdapter.setSelectedType(transactionType.get(i).getType());
            }
        }
    }

    private void setYear(String year, String month) {
        for (int i = 0; i < yearList.size(); i++) {
            if (year.equalsIgnoreCase(yearList.get(i).getYear())) {
                selectedYear = yearList.get(i).getYear();
            }

            for (int j = 0; j < yearList.get(i).getListMonth().size(); j++) {
                if (month.equalsIgnoreCase(yearList.get(i).getListMonth().get(j).getMonth())) {
                    selectedMonth = yearList.get(i).getListMonth().get(j).getMonthString();
                    selectedMonthInNumber = yearList.get(i).getListMonth().get(j).getMonth();
                }
            }
        }
        binding.tvMonth.setText(selectedMonth + " " + selectedYear);
    }

    private void setEnableButton(boolean isChecked) {
        if (isChecked) {
            binding.btnApply.setEnabled(true);
            binding.btnApply.setOnClickListener(this);
        }
    }

    private void setOnClickListener() {
        binding.llSelectDate.setOnClickListener(this);
        binding.rlSelectMonth.setOnClickListener(this);
        binding.toolbar.textRight.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_select_month:
                ListMonthFragment fragment = new ListMonthFragment(this, getApplicationContext(), yearList, selectedMonth, selectedYear);
                fragment.show(getSupportFragmentManager(), "");
                break;
            case R.id.ll_select_date:
                selectStartDate();
                break;
            case R.id.btn_apply:
                submitFilter();
                break;
            case R.id.textRight:
                isReset = true;
                updateViewAfterReset();
                break;
            default:
                break;
        }
    }

    private void resetFilter() {
        Intent returnIntent = new Intent();
        if (transactionTypeId!=null){
            switch(transactionTypeId){
                case Constant.EMAS_SELL_TYPE, Constant.EMAS_BUY_TYPE, Constant.EMAS_MOLDING_TYPE:
                    returnIntent.putExtra(Constant.TRANSACTION_TYPE_ID, transactionTypeId);
                    returnIntent.putExtra(Constant.TRANSACTION_TYPE_NAME, transactionTypeName);
                    setResult(Activity.RESULT_OK, returnIntent);
                    finish();
                    return;
                default:
                    returnIntent.putExtra(Constant.TAG_VALUE, 0);
                    setResult(Activity.RESULT_OK, returnIntent);
                    finish();
            }
        }else {
            returnIntent.putExtra(Constant.TAG_VALUE, 0);
            setResult(Activity.RESULT_OK, returnIntent);
            finish();
        }
    }

    private void updateViewAfterReset() {
        binding.rgFilter.clearCheck();
        binding.rbToday.setChecked(false);
        binding.rbSevenDays.setChecked(false);
        binding.rbSelectMonth.setChecked(false);
        binding.rbSelectDate.setChecked(false);
        binding.llSelectDate.setVisibility(View.GONE);
        binding.rlSelectMonth.setVisibility(View.GONE);
        transactionTypeName = null;
        transactionTypeId = null;
        chipAdapter.setSelectedType(null);
//        binding.btnApply.setTextColor(getResources().getColor(R.color.neutralLight60));
//        binding.btnApply.setBackground(getResources().getDrawable(R.drawable.bg_button_disable));
        if (selectedFilterIs == null && transactionTypeId == null) {
            binding.btnApply.setEnabled(false);
        }
        binding.btnApply.setOnClickListener(this);
    }

    private void submitData() {

        Intent returnIntent = new Intent();

        if (binding.rbToday.isChecked()) {
            returnIntent.putExtra(Constant.FILTER, Constant.TODAY_FILTER);
        } else if (binding.rbSevenDays.isChecked()) {
            returnIntent.putExtra(Constant.FILTER, Constant.SEVEN_DAY_FILTER);
        } else if (binding.rbSelectMonth.isChecked()) {
            returnIntent.putExtra(Constant.FILTER, Constant.MONTH_FILTER);
            if (selectedMonthInNumber != null) {
                returnIntent.putExtra(Constant.MONTH, selectedMonthInNumber);
                returnIntent.putExtra(Constant.MONTH_TEXT, selectedMonth);
                returnIntent.putExtra(Constant.YEAR, selectedYear);
            } else {
                returnIntent.putExtra(Constant.MONTH, CalendarHelper.getCurrentMonth());
                returnIntent.putExtra(Constant.MONTH_TEXT, CalendarHelper.getNameOfCurrentMonth());
                returnIntent.putExtra(Constant.YEAR, CalendarHelper.getCurrentYear());
            }
        } else if (binding.rbSelectDate.isChecked()) {
            returnIntent.putExtra(Constant.FILTER, Constant.RANGE_FILTER);
            if (currentDuration == null) {
                currentDuration = CalendarHelper.getDurasiHariIni();
            }
            if (currentDuration.getStartDay() != 0) {
                if (currentDuration.getEndDay() == 0) {
                    returnIntent.putExtra(Constant.TAG_END_DATE, currentDurationTemp.getEndDateFormatRange());
                } else {
                    returnIntent.putExtra(Constant.TAG_END_DATE, currentDuration.getEndDateFormatRange());
                }
                returnIntent.putExtra(Constant.TAG_START_DATE, currentDuration.getStartDateFormatRange());
            } else {
                returnIntent.putExtra(Constant.TAG_START_DATE, CalendarHelper.getDateNowFormatRange());
                returnIntent.putExtra(Constant.TAG_END_DATE, CalendarHelper.getDateNowFormatRange());
            }
        }
        returnIntent.putExtra(Constant.TRANSACTION_TYPE_ID, transactionTypeId);
        returnIntent.putExtra(Constant.TRANSACTION_TYPE_NAME, transactionTypeName);
        returnIntent.putExtra(Constant.TAG_VALUE, 0);
        setResult(RESULT_OK, returnIntent);
        finish();

    }

    private void submitFilter() {

        if (isReset) {
            resetFilter();
        } else {
            submitData();
        }

    }

    private void selectStartDate() {
        CalendarMutationFragment calendarFragment;
        calendarFragment = new CalendarMutationFragment(this);
        Bundle args = new Bundle();
        if (currentDuration != null) {
            if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
                args.putString(Constant.TAG_START_DATE, currentDuration.getStartDateString());
            }

            if (currentDuration.getEndDateString() != null && !currentDuration.getEndDateString().isEmpty() && currentDuration.getEndMonth() > 0) {
                args.putString(Constant.TAG_END_DATE, currentDuration.getEndDateString());
            }
        }

        if (mIsMutationForRDN) {
            args.putBoolean(Constant.TAG_CUSTOM_CALENDAR, true);
            args.putBoolean(Constant.TAG_CALENDAR_FOR_RDN, true);
        }

        args.putBoolean(Constant.TAG_PICK_START_DATE, true);
        args.putBoolean(Constant.TAG_PICK_DATE, true);
        args.putBoolean(Constant.TAG_MAX_TODAY, true);
        calendarFragment.setArguments(args);

        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    private void selectEndDate() {
        CalendarMutationFragment calendarFragment;
        calendarFragment = new CalendarMutationFragment(this);
        Bundle args = new Bundle();
        if (currentDuration != null) {
            if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
                args.putString(Constant.TAG_START_DATE, currentDuration.getStartDateString());
            }

            if (currentDuration.getEndDateString() != null && !currentDuration.getEndDateString().isEmpty() && currentDuration.getEndMonth() > 0) {
                args.putString(Constant.TAG_END_DATE, currentDuration.getEndDateString());
            }
        }

        if (mIsMutationForRDN) {
            args.putBoolean(Constant.TAG_CUSTOM_CALENDAR, true);
            args.putBoolean(Constant.TAG_CALENDAR_FOR_RDN, true);
        }

        args.putBoolean(Constant.TAG_PICK_END_DATE, true);
        args.putBoolean(Constant.TAG_PICK_DATE, true);
        args.putBoolean(Constant.TAG_MAX_TODAY, true);
        calendarFragment.setArguments(args);

        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    @Override
    public void onSelectStart(@NonNull LocalDate dateSelect) {
        currentDuration = new DurasiModel(
                dateSelect.getDayOfMonth(),
                dateSelect.getMonthValue(),
                dateSelect.getYear());
        setStart(currentDuration);
    }

    public void setStart(DurasiModel durasi) {
        currentDuration = durasi;
        String tanggalString = currentDuration.getStartDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(6, 11);
        binding.tvDate.setText(tanggalString);
    }

    public void setEnd(DurasiModel durasi) {
        currentDuration = durasi;
        String tanggalString = currentDuration.getEndDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(6, 11);
        binding.llSelectDate.setVisibility(View.VISIBLE);
        binding.rlSelectMonth.setVisibility(View.GONE);
    }

    @Override
    public void onSelectEnd(@NonNull LocalDate dateSelect) {
        // Mendapatkan tanggal dari TextView 1 (start date)
        LocalDate startDate = LocalDate.parse(binding.tvDate.getText().toString());

        // Jika tanggal yang dipilih adalah sebelum tanggal start, maka gunakan tanggal tersebut sebagai start date
        if (dateSelect.isBefore(startDate)) {
            binding.tvDate.setText(dateSelect.toString());
            startDate = dateSelect;
        }

        // Buat objek DurasiModel dengan end date yang baru
        currentDuration.setEndDay(dateSelect.getDayOfMonth());
        currentDuration.setEndMonth(dateSelect.getMonthValue());
        currentDuration.setEndYear(dateSelect.getYear());

        // Panggil metode updateDateRange dengan DurasiModel yang diperbarui
        updateDateRange(currentDuration);
    }

    private void updateDateRange(DurasiModel durasiModel) {
        // Lakukan pembaruan atau tindakan yang diperlukan dengan rentang tanggal yang baru
        // Misalnya, Anda dapat memperbarui teks pada TextView atau melakukan operasi lainnya
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
        String formattedStartDate = durasiModel.getStartDatePfmString();
        String formattedEndDate = durasiModel.getEndDatePfmString();

        binding.tvSelectDate.setText(formattedStartDate + " - " + formattedEndDate);
    }

    @Override
    public void onSelectRange(@NonNull LocalDate startDateSelect, @NonNull LocalDate endDateSelect) {
        currentDuration = new DurasiModel(startDateSelect.getDayOfMonth(), startDateSelect.getMonthValue(),
                startDateSelect.getYear(), endDateSelect.getDayOfMonth(), endDateSelect.getMonthValue(), endDateSelect.getYear());

        String startDateString = currentDuration.getStartDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getStartDateMutasiStringddMMMyyyy().substring(6, 11);

        String endDateString = currentDuration.getEndDateMutasiStringddMMMyyyy().substring(0, 3) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(3, 6) +
                currentDuration.getEndDateMutasiStringddMMMyyyy().substring(6, 11);

        binding.llSelectDate.setVisibility(View.VISIBLE);
        binding.rlSelectMonth.setVisibility(View.GONE);
        binding.tvDate.setText(startDateString + " - " + endDateString);
    }

    @Override
    public void onSelectMonth(String month, String year) {
        for (int i = 0; i < yearList.size(); i++) {
            listMonth = yearList.get(i).getListMonth();
            for (int j = 0; j < listMonth.size(); j++) {
                if (month.equalsIgnoreCase(listMonth.get(j).getMonthString())) {
                    monthNumb = listMonth.get(j).getMonth();
                }
            }
        }
        binding.rlSelectMonth.setVisibility(View.VISIBLE);
        binding.llSelectDate.setVisibility(View.GONE);
        selectedMonth = month;
        selectedYear = year;
        selectedMonthInNumber = monthNumb;
        binding.tvMonth.setText(selectedMonth + " " + selectedYear);
    }

    @Override
    public void onDismiss() {
        isCheckedRange = false;
        binding.rlSelectMonth.setVisibility(View.VISIBLE);
        binding.llSelectDate.setVisibility(View.GONE);
    }

    @Override
    public void onSelectType(TransactionTypeModel transactionTypeModel) {
        transactionTypeId = transactionTypeModel.getType();
        transactionTypeName = transactionTypeModel.getTypeDesc();
//        binding.tvType.setText(transactionTypeName);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK && data != null) {
            if (requestCode == Constant.REQ_CALENDAR) {

                // char a = data.getCharExtra(Constant.START_DAY,'a');
                if (data.getIntExtra(Constant.START_DAY, 0) > 0) {
                    currentDuration.setStartDay(data.getIntExtra(Constant.START_DAY, 0));
                    currentDuration.setStartMonth(data.getIntExtra(Constant.START_MONTH, 0));
                    currentDuration.setStartYear(data.getIntExtra(Constant.START_YEAR, 0));
                }

                if (data.getIntExtra(Constant.END_DAY, 0) > 0) {
                    currentDuration.setEndDay(data.getIntExtra(Constant.END_DAY, 0));
                    currentDuration.setEndMonth(data.getIntExtra(Constant.END_MONTH, 0));
                    currentDuration.setEndYear(data.getIntExtra(Constant.END_YEAR, 0));
                }

                //uodate option date Field
                setDefaultDate(currentDuration);
            }
        }
    }

    private void setDefaultDate(DurasiModel durasiModel) {
        if (durasiModel.getStartMonth() > 0) {
            binding.tvDate.setText(durasiModel.getStartDatePfmString() + " - " + durasiModel.getEndDatePfmString());
        }

        if (durasiModel.getEndMonth() > 0) {
            binding.tvDate.setText(durasiModel.getStartDatePfmString() + " - " + durasiModel.getEndDatePfmString());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

    @Override
    public void onDismissDate() {
        isCheckedRange = false;
        binding.llSelectDate.setVisibility(View.VISIBLE);
        binding.rlSelectMonth.setVisibility(View.GONE);
    }
}